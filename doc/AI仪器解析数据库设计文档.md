# AI仪器解析数据库设计文档

## 1. 数据库设计规范

### 1.1 命名规范
- 数据库表名使用全小写，以`tb_edbp`开头
- 字段名使用小驼峰命名方式，不使用下划线分隔
- 主键统一使用`id`字段，类型为`VARCHAR(50)`
- 所有表必须包含标准审计字段

### 1.2 标准字段
每个表必须包含以下标准字段：
```sql
id               VARCHAR(50)  NOT NULL COMMENT '主键',
orgId            VARCHAR(50)  NOT NULL COMMENT '所属机构ID',
domainId         VARCHAR(50)  NOT NULL COMMENT '所属实验室ID',
creator          VARCHAR(50)  NOT NULL COMMENT '创建人',
createDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
modifier         VARCHAR(50)  NOT NULL COMMENT '更新人',
modifyDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
```

## 2. AI仪器解析配置相关表设计

### 2.1 仪器解析配置表（tb_parse_AiInstrumentConfig）

#### 2.1.1 表结构
```sql
CREATE TABLE tb_parse_AiInstrumentConfig (
    id               VARCHAR(50)  NOT NULL COMMENT '主键',
    orgId            VARCHAR(50)  NOT NULL COMMENT '所属机构ID',
    domainId         VARCHAR(50)  NOT NULL COMMENT '所属实验室ID',
    instrumentType   VARCHAR(20)  NOT NULL COMMENT '仪器类型：1-现场仪器，2-实验室仪器',
    instrumentName   VARCHAR(200) NOT NULL COMMENT '仪器名称',
    instrumentCode   VARCHAR(100) NULL COMMENT '仪器编号',
    belongUnit       VARCHAR(200) NULL COMMENT '所属单位',
    promptText       TEXT         NULL COMMENT '提示词内容',
    isEnabled        TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    isDeleted        TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    creator          VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier         VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI仪器解析配置表';
```

#### 2.1.2 字段说明
| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | VARCHAR | 50 | 是 | - | 主键，UUID |
| orgId | VARCHAR | 50 | 是 | - | 所属机构ID |
| domainId | VARCHAR | 50 | 是 | - | 所属实验室ID |
| instrumentType | VARCHAR | 20 | 是 | - | 仪器类型：1-现场仪器，2-实验室仪器 |
| instrumentName | VARCHAR | 200 | 是 | - | 仪器名称 |
| instrumentCode | VARCHAR | 100 | 否 | - | 仪器编号 |
| belongUnit | VARCHAR | 200 | 否 | - | 所属单位（预留字段） |
| promptText | TEXT | - | 否 | - | AI解析提示词内容 |
| isEnabled | TINYINT | 1 | 是 | 1 | 是否启用：0-禁用，1-启用 |
| isDeleted | TINYINT | 1 | 是 | 0 | 逻辑删除标识 |
| creator | VARCHAR | 50 | 是 | - | 创建人 |
| createDate | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| modifier | VARCHAR | 50 | 是 | - | 更新人 |
| modifyDate | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

#### 2.1.3 示例数据
```sql
INSERT INTO tb_parse_AiInstrumentConfig (
    id, orgId, domainId, instrumentType, instrumentName, instrumentCode, 
    belongUnit, promptText, creator, modifier
) VALUES (
    'uuid-001', 'org-001', 'domain-001', '2', '7890A气相色谱仪', 'GC-7890A-001',
    '分析实验室', 
    '你需要对知识库中的数据进行解析，提取成json格式.
需要提取的内容包括：样品编号、分析项目、参数名称、参数值、单位。
json示例： { "sample_code" : "xxx", "AnalyzeItem" ："xxx", "parameter_value"："xxx", "unit":"xxx" }
注意事项：
1、每个参数结果为一块
2、Sample Name为样品编号
3、Peak Name为分析项目
4、Area、Height、Amount分别为峰面积、峰高、测得结果',
    'admin', 'admin'
);
```
## 3. 其他相关表设计（预留）

### 3.1 AI解析模块主表（预留）
```sql
-- 预留：AI解析模块主表设计
-- CREATE TABLE tb_edbp_ai_parse_module (
--     待补充...
-- );
```

### 3.2 解析日志表（预留）
```sql
-- 预留：解析日志表设计（已存在日志模块）
-- CREATE TABLE tb_edbp_ai_parse_log (
--     待补充...
-- );
```

### 3.3 文件管理表（预留）
```sql
-- 预留：文件管理表设计
-- CREATE TABLE tb_edbp_ai_file_manage (
--     待补充...
-- );
```
## 4. 数据字典

### 4.1 仪器类型枚举
| 值 | 说明 |
|----|------|
| 1 | 现场仪器 |
| 2 | 实验室仪器 |

### 4.2 解析方式枚举
| 值 | 说明 |
|----|------|
| 1 | 图像识别 |
| 2 | 文本提取 |

### 4.3 解析状态枚举
| 值 | 说明 |
|----|------|
| 1 | 待解析 |
| 2 | 解析中 |
| 3 | 解析成功 |
| 4 | 解析失败 |
