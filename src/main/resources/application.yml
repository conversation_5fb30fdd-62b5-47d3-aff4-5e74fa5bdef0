server:
  port: ${PORT:8980}
frame-boot:
  swagger: 
    enabled: true
    basePackage: com.sinoyd.boot
    title: 云框架
    description: Rest API
    #termsOfServiceUrl: http://www.sinoyd.com
    #license: license
    #licenseUrl: http://www.sinoyd.com
    version: 1.0
    authorName: zhangjp
    authorEmail: <EMAIL>
  # 框架注册码服务校验地址
  regVerifyUrl: ${REG_VERIFY_URL:http://*************:8760/api/register/auth/verify}
  # 权限同步网关服务地址前缀
  gateUrlPrefix: ${GATE_URL_PREFIX:http://localhost:9090/api/proxy}
  restRequestTimeout: 3000
  # 用户中心相关配置
  user-center:
    # 是否为本地离线模式(true: 离线模式、false：云模式)
    localMode: ${USER_CENTER_LOCALMODE:true}
    # 用户中心接口服务地址
    serverUrl: ${USER_CENTER_SERVERURL:http://*************:8850/api/auth/users}
    # 客户端ID(云版模式需要配置)
    clientId: ${USER_CENTER_CLIENTID:8}
    # 客户端秘钥(云版模式需要配置)
    clientSecret: ${USER_CENTER_CLIENTSECRET:jIkjGEIfn7lYem52gcnfqHiLGkAEav98DfI5BRt4}
    # 短信内容中系统名称(云版模式需要配置)
    sendSmsProductName: ${USER_CENTER_SMSNAME:云框架}
  # 用户登录失败锁相关配置
  user-lock: 
    enabled: false
    # 连续登录失败周期时长(单位：分钟)
    cycleTime: 30
    # 最大失败次数
    maxFailNum: 3
    # 锁定时长(单位：分钟)
    lockTime: 15
  # 拦截器匹配url配置
  interceptor: 
    # 用户token拦截Url(多个用英文,分割不包含空格)
    userIncludePatterns: 
    # 服务token拦截Url(多个用英文,分割不包含空格)
    serviceIncludePatterns: 
    # 用户、服务token排除拦截Url(多个用英文,分割不包含空格)
    excludePathPatterns: 

api: 
  # rest接口添加统一前缀
  prefix: /api
  
# spring
spring:
  autoconfigure: 
    # 排除自动加载的类
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  profiles: 
    # 加载业务系统需要覆盖的配置项
    include: mysql
  application: 
    name: sinoyd-parse
  jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
      #default-property-inclusion: non_null
  cache: 
    type: redis
  redis: 
    host: ${REDIS_HOST:**************}
    password: ${REDIS_PWD:sinoyd}
    timeout: 10000
    database: ${REDIS_DB:3}
    pool: 
      # 连接池最大连接数（使用负值表示没有限制） 默认 8
      max-active: 8
      # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
      max-wait: -1
      # 连接池中的最大空闲连接 默认 8
      max-idle: 8
      # 连接池中的最小空闲连接 默认 0
      min-idle: 0

  # DATABASE CONFIG
  datasource:
    primary: # 业务的数据源
      # mysql连接配置
#      driver-class-name: com.mysql.jdbc.Driver
#      url: *************************************************************************
#      username: root
#      password: 11111
# mysql中确认一下编码
#SHOW VARIABLES LIKE 'char%';
#set character_set_database=utf8;
#set character_set_server=utf8;

      # sql连接配置
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      # url: *********************************************************************
      # username: sa #oracle #devuser
      # password: Sinoyd123
      url: jdbc:sqlserver://*************\MYSQLSERVER2012;DatabaseName=LIMSJAVA
      username: sa #oracle #devuser
      password: qwe123!@#
      # url: ************************************************************
      # username: sa #oracle #devuser
      # password: Sinoyd123
      initialSize: 10
      minIdle: 5
      maxActive: 50
      maxWait: 60000
      testWhileIdle: true
    frame:
      #driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      driverClassName: com.mysql.jdbc.Driver
      #url: jdbc:sqlserver://${DB_HOST:localhost}:${DB_PORT:1433};DatabaseName=${DB_NAME:framecloudboot}
      url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:framecloudboot}?useUnicode=true&characterEncoding=utf-8&useSSL=true
      username: ${DB_USER:frame}
      password: ${DB_PWD:frame}
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 5
      minIdle: 5
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,wall,log4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      useGlobalDataSourceStat: true

  jpa:
#      database: sql_server # oracle严重注意！
 #     database-platform: org.hibernate.dialect.Oracle12cDialect # oracle 12c 严重注意！
 #     database-platform: com.sinoyd.frame.CustomOracle12cDialect # oracle 12c 严重注意！
 #     database-platform: com.sinoyd.frame.CustomMySQLDialect # mysql  严重注意！
 #     database-platform: org.hibernate.dialect.SQLServer2012Dialect # sqlserver  严重注意！
      database-platform: com.sinoyd.frame.configuration.CustomSQLServer2008JSONDialect # sqlserver  严重注意！
      hibernate:
 #       ddl-auto: update
        naming:
          implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
          physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#      show-sql: true
      properties:
        hibernate:
          session_factory:
            statement_inspector: com.sinoyd.frame.inspector.JpaInterceptor
          format_sql: true

#mybatis
mybatis-plus:
  #是否打开SQL执行效率插件【生产环境建议关闭】打开会影响性能
  openSqlPerfStat: false
  mapper-locations: classpath:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sinoyd.boot.frame.sys.model
  typeEnumsPackage: com.sinoyd.boot.common.model.enums
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 2
    #字段策略 0:"忽略判断",1:"非 NULL判断",2:"非空判断"
    field-strategy: 1
    #驼峰下划线转换
    db-column-underline: false
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    #capital-mode: true
    #序列接口实现类配置
    #key-generator: com.baomidou.springboot.xxx
    #逻辑删除配置
    logic-delete-value: 1
    logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: false
    cache-enabled: false

fileProps:
  filePath: d:/files

logging:
  level: info
  file: d:/SpringBoot/SinoydLIMS.log

publish:
  channel: SinoydLimsFileParseExecute

management:
  security:
    enabled: false

#用户和服务调用jwt相关设置    
jwt:
  user:
    token-header: Authorization
    # 有效期（单位：分钟）
    expire: 30
    rsa-secret: xx1WET12^%3^(WE45
  client:
    rsa-secret: x2318^^(*WRYQWR(QW&T