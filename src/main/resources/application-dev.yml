#app
server:
  port: ${PORT:8980}
  grpcPort: ${GRPC_PORT:8911}
  tomcat:
    uri-encoding: utf-8

frame-boot:
  swagger:
    enabled: true
    # controller包路径
    basePackage: com.sinoyd
    title: 快速开发基础平台
    description: 系统Rest接口API
  # 框架注册码服务校验地址
  regVerifyUrl: ${REG_VERIFY_URL:http://*************:8760/api/register/auth/verify}
  # 权限同步网关服务地址前缀
  gateUrlPrefix: ${GATE_URL_PREFIX:http://localhost:9090/api/proxy}
  # rest请求超时设置(单位：毫秒)
  restRequestTimeout: 3000
  # 互联网用户中心相关配置
  user-center:
    # 是否为本地离线模式(true: 离线模式、false：云模式)
    localMode: ${USER_CENTER_LOCALMODE:true}
    # 用户中心接口服务地址
    serverUrl: ${USER_CENTER_SERVERURL:http://*************:8850/api/auth/users}
    # 客户端ID(云版模式需要配置)
    clientId: ${USER_CENTER_CLIENTID:8}
    # 客户端秘钥(云版模式需要配置)
    clientSecret: ${USER_CENTER_CLIENTSECRET:jIkjGEIfn7lYem52gcnfqHiLGkAEav98DfI5BRt4}
    # 短信内容中系统名称(云版模式需要配置)
    sendSmsProductName: ${USER_CENTER_SMSNAME:云框架}
  # 用户登录失败锁相关配置
  user-lock:
    enabled: false

# spring
spring:
  application:
    name: sinoyd-parse
  redis:
    host: ${REDIS_HOST:*************}
    password: sinoyd
    timeout: 100000
    database: 6

#  host: ${REDIS_HOST:**************}
#  password: sinoyd
#  timeout: 100000
#  database: 6

  # DATABASE CONFIG
  datasource:
    primary: # 业务的数据源
      # mysql连接配置
#      driver-class-name: com.mysql.jdbc.Driver
#      url: *************************************************************************
#      username: root
#      password: 11111
# mysql中确认一下编码
#SHOW VARIABLES LIKE 'char%';
#set character_set_database=utf8;
#set character_set_server=utf8;

      # sql连接配置
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      url: ***********************************************************
#      url: ********************************************************
#       url: ************************************************************** # *******************************************   #*********************************************** #
#      url: ************************************************************
      username: sa #oracle #devuser
      password: 1qaz
#      url: ***********************************************************
#      username: appuser #oracle #devuser
#      password: 11111
      initialSize: 10
      minIdle: 5
      maxActive: 50
      maxWait: 60000
      testWhileIdle: true
      # password: 11111 #sa11111  #manager1 #qweAsd#21 #123qwe!@#
    # 框架的数据源
    frame:
      # driverClassName: com.mysql.jdbc.Driver
      # url: jdbc:mysql://${MYSQL_HOST:*************}:${MYSQL_PORT:3306}/framecloudboot?useUnicode=true&characterEncoding=utf-8&useSSL=true
      driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      url: jdbc:sqlserver://${SQLSER_HOST:*************};DatabaseName=FrameInstrumentParse
#      url: jdbc:sqlserver://${SQLSER_HOST:127.0.0.1\SQL2012};DatabaseName=frameCloudBoot
      username: sa
      password: 1qaz
#      url: jdbc:sqlserver://${SQLSER_HOST:************\sql2012};DatabaseName=frameCloudBoot
#      username: appuser
#      password: 11111
      maxActive: 100
      poolPreparedStatements: false

  jpa:
     database: sql_server # oracle严重注意！
#     database-platform: org.hibernate.dialect.Oracle12cDialect # oracle 12c 严重注意！
#     database-platform: com.sinoyd.frame.CustomOracle12cDialect # oracle 12c 严重注意！
#     database-platform: com.sinoyd.frame.CustomMySQLDialect # mysql  严重注意！
#     database-platform: org.hibernate.dialect.SQLServer2012Dialect # sqlserver  严重注意！
     database-platform: com.sinoyd.frame.configuration.CustomSQLServer2008JSONDialect # sqlserver  严重注意！
     hibernate:
#       ddl-auto: update
       naming:
         implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
         physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
     show-sql: true
     properties:
       hibernate:
         session_factory:
           statement_inspector: com.sinoyd.frame.inspector.JpaInterceptor
         format_sql: true

  http:
  multipart:
    max-file-size: 10000MB
    max-request-size: 10000MB
    enabled: true

fileProps:
  filePath: E:/lims/project/LIMSJAVA/proxy/files

logging:
  level: info http://localhost:8888/lims/

  file: d:/SpringBoot/SinoydLIMS.log

publish:
  channel: SinoydLimsFileParseExecute

management:
  security:
    enabled: false

#用户和服务调用jwt相关设置
jwt:
  user:
    token-header: Authorization
    # 有效期（单位：分钟）
    expire: 120
